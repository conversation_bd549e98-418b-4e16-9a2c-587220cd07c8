import re

# Test data from your example
test_message = """
📈 Vol: 1h: $21 | 1d: $63.4K

❌ Dex Paid
⚡️ Scans: 47 | 🔗 X (https://x.com/DogemonOnSol)•WEB (https://dogemononsol.xyz/)
👥 Hodls (https://solscan.io/token/BDw1Z9Dz2AkzMJPmiMQTB7yeVqQrb2uPCLMFT78Bpump#holders): 36 • Top: 13.6%
┗ High: 5.2% (https://solscan.io/account/2PdJ1PnjA8UTWamAh5PEev7nouVBairLvxAXSCfNGTJt)
"""

def test_updated_patterns():
    """Test the updated extraction patterns"""
    print("Testing Updated Extraction Patterns:")
    print("=" * 50)
    
    # Test Volume pattern
    volume_match = re.search(r"📈 Vol: ([^⚡❌\n]+)", test_message)
    if volume_match:
        print(f"✅ Volume: {volume_match.group(1).strip()}")
    else:
        print("❌ Volume: No match")
    
    # Test Scans pattern
    scans_match = re.search(r"⚡️ Scans: (\d+)", test_message)
    if scans_match:
        print(f"✅ Scans: {scans_match.group(1)}")
    else:
        print("❌ Scans: No match")
    
    # Test Hodls pattern - Debug
    print(f"Debug - Looking for Hodls in: {test_message}")
    hodls_match = re.search(r"👥 Hodls \([^)]+\): (\d+)", test_message)
    if hodls_match:
        print(f"✅ Hodls: {hodls_match.group(1)}")
    else:
        print("❌ Hodls: No match")

    # Test Top Holders pattern
    top_holders_match = re.search(r"👥 Hodls \([^)]+\): \d+ • Top: (\d+\.?\d*%)", test_message)
    if top_holders_match:
        print(f"✅ Top Holders: {top_holders_match.group(1)}")
    else:
        print("❌ Top Holders: No match")
    
    # Test High pattern
    high_match = re.search(r"┗ High: (\d+\.?\d*%)", test_message)
    if high_match:
        print(f"✅ High: {high_match.group(1)}")
    else:
        print("❌ High: No match")
    
    # Test Dex Paid pattern
    if "❌" in test_message and "Dex" in test_message:
        print("✅ Dex Paid: ❌")
    elif "✅" in test_message and "Dex" in test_message:
        print("✅ Dex Paid: ✅")
    else:
        print("❌ Dex Paid: No match")

def test_expected_results():
    """Show expected results"""
    print("\nExpected Results:")
    print("=" * 50)
    print("Volume: 1h: $21 | 1d: $63.4K")
    print("Scans: 47")
    print("Hodls: 36")
    print("Top Holders: 13.6%")
    print("High: 5.2%")
    print("Dex Paid: ❌")

if __name__ == "__main__":
    test_updated_patterns()
    test_expected_results()
