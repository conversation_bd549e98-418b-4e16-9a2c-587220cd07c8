from telethon import TelegramClient
import asyncio
import re
import json
from datetime import datetime

# Configuration
API_ID = "24841946"
API_HASH = "38d74f00caf1381396fc58a579e89a97"
PHONE = "+212762663516"
SCANNER_BOT = "@soul_scanner_bot"

async def analyze_detailed_patterns():
    """Fetch and analyze detailed patterns from soul_scanner_bot messages"""
    
    client = TelegramClient('session', API_ID, API_HASH)
    
    try:
        await client.start(phone=PHONE)
        print(f"✅ Connected to Telegram")
        print(f"🔍 Fetching last 10 messages from {SCANNER_BOT}...")
        print("=" * 80)
        
        detailed_patterns = []
        message_count = 0
        
        # Fetch recent messages
        async for msg in client.iter_messages(SCANNER_BOT, limit=10):
            if msg.text and len(msg.text) > 100:  # Only analyze substantial messages
                message_count += 1
                
                print(f"\n📨 MESSAGE #{message_count}")
                print(f"🕒 Time: {msg.date}")
                print("-" * 40)
                
                # Extract detailed patterns
                patterns = extract_detailed_patterns(msg.text)
                
                if patterns:
                    print("🔍 DETAILED PATTERNS FOUND:")
                    for pattern, value in patterns.items():
                        print(f"   {pattern}: {value}")
                    
                    # Store message data
                    detailed_patterns.append({
                        'timestamp': msg.date.isoformat(),
                        'patterns': patterns,
                        'message_preview': msg.text[:200] + "..."
                    })
                else:
                    print("   No detailed patterns found")
                
                print("-" * 40)
        
        # Save detailed analysis
        with open('detailed_patterns_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(detailed_patterns, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Analyzed {message_count} messages")
        print(f"💾 Detailed patterns saved to 'detailed_patterns_analysis.json'")
        
        # Generate summary
        generate_pattern_summary(detailed_patterns)
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        await client.disconnect()
        print("🔌 Disconnected")

def extract_detailed_patterns(text):
    """Extract detailed patterns from scanner message"""
    patterns = {}
    
    # 🎯 First 20 patterns
    first20_patterns = [
        r"🎯 First 20[^:]*: (\d+%)",
        r"\*\*🎯 First 20\*\*[^:]*: (\d+%)",
        r"First 20[^:]*: (\d+%)"
    ]
    
    for pattern in first20_patterns:
        match = re.search(pattern, text)
        if match:
            patterns['First 20 Percentage'] = match.group(1)
            break
    
    # Extract the detailed First 20 line with fish/whale counts
    first20_detailed = re.search(r"🎯 First 20[^:]*: (\d+%) \| (\d+) ([🛠🐟🍤🐳🌱]+) • (\d+%)", text)
    if first20_detailed:
        patterns['First 20 Percentage'] = first20_detailed.group(1)
        patterns['First 20 Count'] = first20_detailed.group(2)
        patterns['First 20 Types'] = first20_detailed.group(3)
        patterns['First 20 Secondary Percentage'] = first20_detailed.group(4)
    
    # Made patterns
    made_patterns = [
        r"Made: (\d+)",
        r"\[Made: (\d+)\]"
    ]
    
    for pattern in made_patterns:
        match = re.search(pattern, text)
        if match:
            patterns['Made'] = match.group(1)
            break
    
    # Bond patterns
    bond_match = re.search(r"Bond: (\d+)", text)
    if bond_match:
        patterns['Bond'] = bond_match.group(1)
    
    # Best patterns
    best_match = re.search(r"Best: (\$[^]]+)", text)
    if best_match:
        patterns['Best'] = best_match.group(1).strip()
    
    # Extract whale/fish pattern sequences
    whale_fish_patterns = extract_whale_fish_patterns(text)
    if whale_fish_patterns:
        patterns.update(whale_fish_patterns)
    
    # Extract holder type counts
    holder_counts = count_holder_types(text)
    if holder_counts:
        patterns.update(holder_counts)
    
    return patterns

def extract_whale_fish_patterns(text):
    """Extract whale/fish emoji patterns and analyze them"""
    patterns = {}
    
    # Find lines with whale/fish emojis
    whale_fish_lines = re.findall(r'[🛠🐟🍤🐳🌱]+', text)
    
    if whale_fish_lines:
        # Combine all patterns
        all_patterns = ''.join(whale_fish_lines)
        patterns['Whale Fish Pattern'] = all_patterns
        
        # Count each type
        dev_count = all_patterns.count('🛠')
        fish_count = all_patterns.count('🐟')
        shrimp_count = all_patterns.count('🍤')
        whale_count = all_patterns.count('🐳')
        fresh_count = all_patterns.count('🌱')
        
        if dev_count > 0:
            patterns['Dev Count'] = str(dev_count)
        if fish_count > 0:
            patterns['Fish Count'] = str(fish_count)
        if shrimp_count > 0:
            patterns['Shrimp Count'] = str(shrimp_count)
        if whale_count > 0:
            patterns['Whale Count'] = str(whale_count)
        if fresh_count > 0:
            patterns['Fresh Count'] = str(fresh_count)
        
        # Calculate percentages
        total_holders = dev_count + fish_count + shrimp_count + whale_count + fresh_count
        if total_holders > 0:
            patterns['Total First 20 Holders'] = str(total_holders)
            if whale_count > 0:
                patterns['Whale Percentage'] = f"{(whale_count/total_holders)*100:.1f}%"
            if fish_count > 0:
                patterns['Fish Percentage'] = f"{(fish_count/total_holders)*100:.1f}%"
            if shrimp_count > 0:
                patterns['Shrimp Percentage'] = f"{(shrimp_count/total_holders)*100:.1f}%"
    
    return patterns

def count_holder_types(text):
    """Count different holder types from the message"""
    patterns = {}
    
    # Look for specific holder type mentions
    holder_patterns = [
        (r'(\d+) 🛠', 'Dev Holders'),
        (r'(\d+) 🐟', 'Fish Holders'),
        (r'(\d+) 🍤', 'Shrimp Holders'),
        (r'(\d+) 🐳', 'Whale Holders'),
        (r'(\d+) 🌱', 'Fresh Holders')
    ]
    
    for pattern, name in holder_patterns:
        match = re.search(pattern, text)
        if match:
            patterns[name] = match.group(1)
    
    return patterns

def generate_pattern_summary(detailed_patterns):
    """Generate a summary of all detailed patterns found"""
    
    print(f"\n📊 DETAILED PATTERNS SUMMARY")
    print("=" * 50)
    
    all_patterns = {}
    
    for msg_data in detailed_patterns:
        for pattern, value in msg_data['patterns'].items():
            if pattern not in all_patterns:
                all_patterns[pattern] = []
            all_patterns[pattern].append(value)
    
    for pattern, values in all_patterns.items():
        unique_values = list(set(values))[:5]  # Show first 5 unique values
        print(f"{pattern}: {len(values)} occurrences")
        print(f"   Examples: {unique_values}")
        print()
    
    # Save patterns to file
    with open('detailed_extraction_patterns.json', 'w', encoding='utf-8') as f:
        json.dump(all_patterns, f, indent=2, ensure_ascii=False)
    
    print(f"💾 Detailed pattern analysis saved to 'detailed_extraction_patterns.json'")

if __name__ == "__main__":
    print("🚀 Soul Scanner Bot - Detailed Pattern Analyzer")
    print("=" * 50)
    asyncio.run(analyze_detailed_patterns())
