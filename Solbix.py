from telethon import TelegramClient
import asyncio
import os
import time
import logging
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration settings
API_ID = "24841946"
API_HASH = "38d74f00caf1381396fc58a579e89a97"
PHONE = "+212762663516"

# Destination bot
destination_username = "@soul_scanner_bot"  # Username of the destination bot

# Rate limiting
RATE_LIMIT_DELAY = 2  # Delay between sending tokens

# Global variables
client = None
processed_tokens = set()  # Keep track of processed tokens to avoid duplicates

def read_tokens_from_file():
    """Read tokens from tokens.txt file"""
    tokens = []
    try:
        if os.path.exists("tokens.txt"):
            with open("tokens.txt", "r", encoding="utf-8") as f:
                for line in f:
                    token = line.strip()
                    if token and len(token) >= 32:  # Basic validation for token length
                        tokens.append(token)
            logger.info(f"?? Read {len(tokens)} tokens from tokens.txt")
        else:
            logger.warning("?? tokens.txt file not found")
    except Exception as e:
        logger.error(f"? Error reading tokens.txt: {e}")
    return tokens

async def send_tokens_to_bot():
    """Send all tokens from tokens.txt to the destination bot"""
    tokens = read_tokens_from_file()

    if not tokens:
        logger.warning("?? No tokens found to send")
        return

    sent_count = 0
    for token in tokens:
        if token in processed_tokens:
            logger.info(f"? Token {token[:10]}... already processed, skipping")
            continue

        try:
            logger.info(f"?? Sending token to {destination_username}: {token}")
            await client.send_message(destination_username, token)
            processed_tokens.add(token)
            sent_count += 1

            # Rate limiting
            if sent_count < len(tokens):  # Don't delay after the last token
                logger.info(f"? Waiting {RATE_LIMIT_DELAY} seconds before next token...")
                await asyncio.sleep(RATE_LIMIT_DELAY)

        except Exception as e:
            logger.error(f"? Error sending token {token}: {e}")

    logger.info(f"? Finished sending {sent_count} tokens to {destination_username}")

async def main():
    """Main function"""
    global client

    logger.info("?? Starting Token Sender Bot")
    logger.info(f"?? Destination: {destination_username}")
    logger.info("="*50)

    # Initialize Telegram client
    client = TelegramClient('session', API_ID, API_HASH)

    try:
        # Start the client
        await client.start(phone=PHONE)
        logger.info("? Client started successfully")

        # Send tokens
        await send_tokens_to_bot()

    except Exception as e:
        logger.error(f"? Error in main: {e}")
    finally:
        if client:
            await client.disconnect()
            logger.info("?? Client disconnected")

if __name__ == "__main__":
    asyncio.run(main())





async def message_handler(event):
    """Handle all incoming messages from the source channel and bots."""
    global last_message_time, update_state

    # Update last message time
    last_message_time = time.time()

    # Save update state for persistence
    try:
        if hasattr(event, 'pts'):
            update_state['pts'] = event.pts
        if hasattr(event, 'qts'):
            update_state['qts'] = event.qts
        if hasattr(event, 'date'):
            update_state['date'] = event.date
        if hasattr(event, 'seq'):
            update_state['seq'] = event.seq

        # Periodically save the session state
        if any(update_state.values()):
            save_session_state()
    except Exception as e:
        logger.warning(f"?? Could not update state: {e}")

    # Log the message arrival immediately
    message_text = event.message.text if hasattr(event, 'message') and hasattr(event.message, 'text') else "No text"

    # Determine the source of the message
    chat_username = None

    try:
        chat = await event.get_chat()
        chat_username = chat.username if hasattr(chat, 'username') else str(getattr(chat, 'id', 'unknown'))
    except Exception as e:
        # If we can't get the chat info, use a default
        logger.warning(f"?? Could not get chat info: {e}")
        chat_username = "unknown"

    # Log with source information
    logger.info(f"?? Received message from {chat_username}: {message_text[:30]}...")

    try:
        # Add to processing queue with higher priority for new messages
        await processing_queue.put((0, event))  # Lower number = higher priority
        logger.info(f"?? Queued new message. Current queue size: {processing_queue.qsize()}")
    except Exception as e:
        logger.error(f"? Error queuing message: {e}")
        # Add to buffer if queue is full or other error
        message_buffer.append((0, event))
        logger.warning(f"?? Added message to buffer. Buffer size: {len(message_buffer)}")

async def process_queue_worker():
    """Process messages from the queue one at a time."""
    global is_processing, message_buffer

    while True:
        try:
            # First check if we have any buffered messages to process
            if not is_processing and message_buffer:
                is_processing = True
                _, event = message_buffer.pop(0)  # Using _ to ignore the priority
                logger.info(f"?? Processing message from buffer. Remaining buffer size: {len(message_buffer)}")

                await process_message(event)
                is_processing = False

            # Then check the queue
            elif not is_processing and not processing_queue.empty():
                is_processing = True
                _, event = await processing_queue.get()  # Using _ to ignore the priority

                await process_message(event)
                processing_queue.task_done()
                is_processing = False

            else:
                # No messages to process, sleep briefly
                await asyncio.sleep(0.1)  # Very short sleep to prevent CPU hogging

        except FloodWaitError as e:
            # Handle rate limiting
            wait_time = e.seconds
            logger.warning(f"?? Rate limited! Waiting for {wait_time} seconds")
            await asyncio.sleep(wait_time)
            is_processing = False

        except (ServerError, TimedOutError) as e:
            # Handle network errors
            logger.error(f"? Network error: {e}. Retrying in 5 seconds...")
            await asyncio.sleep(5)
            is_processing = False

        except Exception as e:
            logger.error(f"? Error in queue worker: {e}")
            is_processing = False
            await asyncio.sleep(1)

async def process_message(event):
    """Process a single message."""
    try:
        # Handle both proper Message objects and string objects
        if hasattr(event, 'message'):
            # This is a proper event object
            message = event.message
            message_text = message.text if hasattr(message, 'text') else ""
        elif isinstance(event, str):
            # This is just a string, create a simple object to mimic the message structure
            logger.info(f"Processing string message instead of Message object")
            message_text = event

            # Create a simple object that mimics the necessary attributes
            class SimpleMessage:
                def __init__(self, text):
                    self.text = text
                    self.entities = []

            message = SimpleMessage(message_text)
        else:
            # Neither a proper event nor a string, log and skip
            logger.error(f"Unknown message type: {type(event)}")
            return

        logger.info(f"?? Processing message: {message_text[:50]}...")

        # Extract all URLs and pattern matches from the message
        urls = extract_urls_from_entities(message)

        # Add text URLs that might not be in entities
        text_urls = re.findall(r'https?://\S+', message_text)
        for url in text_urls:
            if url not in urls:
                urls.append(url)

        # Enhanced pattern detection - Check patterns in the entire message
        has_type1_pattern = any(pattern in message_text for pattern in type1_pattern)
        has_type2_pattern = any(pattern in message_text.lower() for pattern in type2_pattern)

        # Extract tokens from URLs
        type1_tokens = []
        type2_tokens = []

        for url in urls:
            # Try Type 1 pattern (Bubblemap URLs in parentheses)
            token = extract_token_from_url(url, token_pattern_type1)
            if token and token not in type1_tokens:
                type1_tokens.append(token)

            # Try Type 2 pattern (GeckoTerminal URLs)
            token_match = re.search(r'geckoterminal\.com/solana/pools/([a-zA-Z0-9]+)', url)
            if token_match:
                token = token_match.group(1)
                if token and token not in type2_tokens:
                    type2_tokens.append(token)

        # Look specifically for Bubblemap URLs in parentheses
        bubblemap_matches = re.findall(r'\(https?://(?:app\.)?bubblemaps\.io/sol/token/([A-Za-z0-9]{43,44})', message_text)
        for token in bubblemap_matches:
            if token and token not in type1_tokens:
                type1_tokens.append(token)

        # Log found tokens for debugging
        if bubblemap_matches:
            logger.info(f"?? Found token addresses from Bubblemap URLs in parentheses: {bubblemap_matches}")

        # Get chat info to determine message source
        chat_username = None
        try:
            if hasattr(event, 'get_chat'):
                chat = await event.get_chat()
                chat_username = chat.username if hasattr(chat, 'username') else str(getattr(chat, 'id', 'unknown'))
        except Exception as e:
            logger.warning(f"?? Could not get chat info: {e}")

        # Check message source
        is_from_tracker_bot = chat_username and destination_username2.replace('@', '') == chat_username
        is_from_source = chat_username and source_username.replace('@', '') == chat_username

        # Process messages based on source and content
        if is_from_tracker_bot:
            # This is from the TokenTrackerbotbot_bot - check for profit updates
            logger.info(f"?? Message from {destination_username2} - checking for profit updates")

            # Check for token addresses in the tracker bot messages
            if type1_tokens:
                logger.info(f"?? Found token addresses in tracker bot message: {type1_tokens}")

            # Process profit updates
            if has_type2_pattern or type2_tokens or "profit" in message_text.lower():
                logger.info("?? Type 2 message detected from tracker bot (profit update)")
                await process_type2_message(message)
            # Process Trailing Stop Loss messages
            elif "Trailing Stop Loss Triggered" in message_text:
                logger.info("?? Trailing Stop Loss message detected from tracker bot")
                await process_type2_message(message)
            # Also process any message with token addresses
            elif type1_tokens:
                logger.info("?? Token addresses found in tracker bot message")
                await process_type2_message(message)
        elif is_from_source:
            # Process messages from source channel
            if has_type1_pattern or type1_tokens:
                logger.info("?? Type 1 message detected (New Trending) from source channel")
                await process_type1_message(message)
            # We don't process profit matches from the source channel anymore
            # Only from TokenTrackerbotbot_bot
        else:
            # More aggressive parsing for missed messages

            # First check for any token addresses in the message
            if type1_tokens:
                if is_from_tracker_bot:
                    logger.info(f"?? Found token addresses in tracker bot message: {type1_tokens}")
                    await process_type2_message(message)
                elif is_from_source:
                    logger.info(f"?? Found token addresses in source message: {type1_tokens}")
                    await process_type1_message(message)
            # Then check for GeckoTerminal URLs in tracker bot messages
            elif is_from_tracker_bot:
                # Look for any GeckoTerminal or Helius XRay URL
                token_urls = []
                for url in urls:
                    if "geckoterminal.com" in url:
                        token_match = re.search(r'pools/([a-zA-Z0-9]+)', url)
                        if token_match:
                            token_urls.append(token_match.group(1))
                    elif "xray.helius.xyz" in url:
                        token_match = re.search(r'token/([A-Za-z0-9]{43,44})', url)
                        if token_match:
                            token_urls.append(token_match.group(1))

                if token_urls:
                    logger.info(f"?? Possible profit update detected from tracker bot (Token URL found: {token_urls})")
                    await process_type2_message(message)
                else:
                    logger.info("? Message from tracker bot does not match any patterns, skipping")
            else:
                logger.info("? Message does not match any patterns or is from the wrong source, skipping")

    except Exception as e:
        logger.error(f"? Error processing message: {e}")
        # Log the full message for debugging
        if 'message_text' in locals():
            logger.error(f"Message content: {message_text}")

        # Save session state on error to ensure we don't lose state
        save_session_state()

async def fetch_recent_messages():
    """Set up message catching without fetching historical messages."""
    try:
        # Instead of fetching historical messages, just get the current state
        # to ensure we don't miss any future messages
        logger.info("?? Setting up message catching (skipping historical messages)...")

        # Get the current update state if available
        try:
            if hasattr(client.session, 'get_update_state') and callable(client.session.get_update_state):
                state = client.session.get_update_state()
                if state:
                    pts, qts, date, seq = state
                    update_state["pts"] = pts
                    update_state["qts"] = qts
                    update_state["date"] = date
                    update_state["seq"] = seq
                    logger.info(f"? Current update state: pts={pts}, qts={qts}, date={date}, seq={seq}")
        except Exception as e:
            logger.warning(f"?? Could not get update state: {e}")

        # Save the session state
        save_session_state()
        logger.info("? Ready to receive new messages")

    except Exception as e:
        logger.error(f"? Error in fetch_recent_messages: {e}")
        # Continue anyway - we'll just start receiving messages from now

async def check_for_missed_messages():
    """Periodic check for missed messages if we haven't received any recently."""
    global last_message_time

    while True:
        try:
            # Wait for the check interval
            await asyncio.sleep(CONNECTION_CHECK_INTERVAL * 60)  # Convert minutes to seconds

            # If no messages received recently, fetch recent messages again
            current_time = time.time()
            if current_time - last_message_time > MISSED_MESSAGE_THRESHOLD:
                logger.warning(f"?? No messages received in the last {MISSED_MESSAGE_THRESHOLD/60:.1f} minutes! Fetching recent messages...")
                await fetch_recent_messages()
                last_message_time = current_time  # Reset timer after fetching
        except Exception as e:
            logger.error(f"? Error in missed message checker: {e}")
            await asyncio.sleep(60)  # Wait a bit after error and try again

async def main():
    """Main entry point for the application."""
    global client, last_message_time

    # Initialize the last_message_time to the current time
    last_message_time = time.time()

    # Print startup information
    print("\n" + "="*50)
    print("Solana Token Tracker Bot")
    print("="*50)
    print(f"Source Channel: {source_username}")
    print(f"Destination Bot 1: {destination_username}")
    print(f"Destination Bot 2: {destination_username2}")
    print(f"Destination Bot 3: {destination_username3}")
    print(f"Destination Bot 4: {destination_username4}")
    print(f"Destination Bot 5: {destination_username5} (with /buy command)")
    print(f"Destination Bot 6: {destination_username6}")
    print(f"Excel File: {EXCEL_FILE}")
    print(f"Using Morocco timezone: {MOROCCO_TZ}")
    print("="*50 + "\n")

    # Initialize Excel file
    initialize_excel()

    # Load session state if available
    load_session_state()

    # Create and start the Telegram client
    logger.info("?? Starting Telegram client...")

    # Try to use a saved session string if available
    session = None
    if os.path.exists(SESSION_STRING_FILE):
        try:
            with open(SESSION_STRING_FILE, 'r') as f:
                session_string = f.read().strip()
                if session_string:
                    session = StringSession(session_string)
                    logger.info("? Loaded session string from file")
        except Exception as e:
            logger.warning(f"?? Could not load session string: {e}")

    # Create the client with the session if available
    if session:
        client = TelegramClient(session, api_id, api_hash)
    else:
        client = TelegramClient("solbix_data", api_id, api_hash)

    try:
        await client.start(phone_number)
        logger.info("? Client started successfully")

        # Save the session string
        if isinstance(client.session, StringSession):
            with open(SESSION_STRING_FILE, 'w') as f:
                f.write(client.session.save())
            logger.info("? Saved session string to file")

        # First fetch recent messages to catch up on any we might have missed
        await fetch_recent_messages()

        # Set up event handler for source channel
        client.add_event_handler(
            message_handler,
            events.NewMessage(chats=[source_username])
        )

        # Set up edited message handler (also important to catch updates)
        client.add_event_handler(
            message_handler,
            events.MessageEdited(chats=[source_username])
        )

        # Set up event handler for the second bot (TokenTrackerbotbot_bot)
        client.add_event_handler(
            message_handler,
            events.NewMessage(chats=[destination_username2])
        )

        # Set up edited message handler for the second bot
        client.add_event_handler(
            message_handler,
            events.MessageEdited(chats=[destination_username2])
        )

        # Start the queue worker
        logger.info("?? Starting queue worker...")
        asyncio.create_task(process_queue_worker())

        # Start the missed message checker
        logger.info("?? Starting missed message checker...")
        asyncio.create_task(check_for_missed_messages())

        logger.info(f"?? Bot is now monitoring {source_username} and {destination_username2} for new and edited messages")

        # Keep the bot running with periodic reconnection checks and state saving
        last_save_time = time.time()
        save_interval = 300  # Save state every 5 minutes

        while True:
            current_time = time.time()

            # Check connection
            if not client.is_connected():
                logger.warning("?? Client disconnected. Attempting to reconnect...")
                await client.connect()

                # Fetch messages after reconnection
                logger.info("?? Reconnected, fetching recent messages...")
                await fetch_recent_messages()

            # Periodically save session state
            if current_time - last_save_time > save_interval:
                logger.info("?? Saving session state...")
                save_session_state()
                last_save_time = current_time

            # Process any buffered messages if the queue is empty
            if not processing_queue.empty() and message_buffer:
                logger.info(f"?? Moving {len(message_buffer)} buffered messages to queue")
                for buffered_message in message_buffer[:]:
                    try:
                        await processing_queue.put(buffered_message)
                        message_buffer.remove(buffered_message)
                    except Exception as e:
                        logger.error(f"? Error moving buffered message to queue: {e}")

            await asyncio.sleep(KEEPALIVE_INTERVAL)  # Check connection more frequently

    except Exception as e:
        logger.error(f"? Error in main: {e}")
    finally:
        # Save session state before disconnecting
        try:
            logger.info("?? Saving final session state...")
            save_session_state()
        except Exception as e:
            logger.error(f"? Error saving final session state: {e}")

        if client.is_connected():
            await client.disconnect()
        logger.info("?? Bot has been stopped")

if __name__ == "__main__":
    # Run the bot
    asyncio.run(main())