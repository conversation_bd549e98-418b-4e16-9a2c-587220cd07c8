from telethon import TelegramClient
import asyncio
import os
import logging
import sys
import re
import time
from openpyxl import Workbook, load_workbook
from openpyxl.styles import PatternFill
from datetime import datetime
import pytz

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# Configuration
API_ID = "24841946"
API_HASH = "38d74f00caf1381396fc58a579e89a97"
PHONE = "+212762663516"
DESTINATION_BOT = "@soul_scanner_bot"
RATE_LIMIT_DELAY = 2  # seconds between tokens

# Excel configuration
EXCEL_FILE = "solbix_data.xlsx"
HEADERS = [
    "Timestamp", "Token Address", "Token Name", "Warnings", "Age", "MC", "T-MC",
    "Liq", "Liq SOL", "Volume", "Price", "Dex Paid", "Scans", "Hodls", "High",
    "Snipers", "Snipers Percentage", "Dev", "Sniped", "Top Holders", "LP Burnt",
    "Mint Authority", "Freeze Authority", "Bundled", "Airdrop", "Burnt", "First",
    "First Percentage", "Sold", "Made", "Bond", "Best", "First 20 Percentage",
    "First 20 Count", "First 20 Types", "Dev Count", "Fish Count", "Shrimp Count",
    "Whale Count", "Fresh Count", "Total First 20", "Whale Percentage",
    "Fish Percentage", "Shrimp Percentage", "Whale Fish Pattern"
]

# Morocco timezone
MOROCCO_TZ = pytz.timezone('Africa/Casablanca')

# Conditional formatting colors
GREY_FILL = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
YELLOW_FILL = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
GREEN_FILL = PatternFill(start_color="00FF00", end_color="00FF00", fill_type="solid")

# Global variables
client = None
processed_tokens = set()
processing_lock = asyncio.Lock()

def initialize_excel():
    """Create Excel file with headers if it doesn't exist"""
    if not os.path.exists(EXCEL_FILE):
        logger.info(f"Creating new Excel file: {EXCEL_FILE}")
        wb = Workbook()
        ws = wb.active
        ws.append(HEADERS)
        wb.save(EXCEL_FILE)
        logger.info(f"✅ Excel file created: {EXCEL_FILE}")

def extract_data_from_scanner_response(message_text, token_address):
    """Extract data from the scanner bot's message."""
    data = {
        "Token Address": token_address,
        "Token Name": "N/A",
        "Warnings": "N/A",
        "Age": "N/A",
        "MC": "N/A",
        "T-MC": "N/A",
        "Liq": "N/A",
        "Liq SOL": "N/A",
        "Volume": "N/A",
        "Price": "N/A",
        "Dex Paid": "N/A",
        "Scans": "N/A",
        "Hodls": "N/A",
        "High": "N/A",
        "Snipers": "N/A",
        "Snipers Percentage": "N/A",
        "Dev": "N/A",
        "Sniped": "N/A",
        "Top Holders": "N/A",
        "LP Burnt": "N/A",
        "Mint Authority": "N/A",
        "Freeze Authority": "N/A",
        "Bundled": "N/A",
        "Airdrop": "N/A",
        "Burnt": "N/A",
        "First": "N/A",
        "First Percentage": "N/A",
        "Sold": "N/A",
        "Made": "N/A",
        "Bond": "N/A",
        "Best": "N/A",
        "First 20 Percentage": "N/A",
        "First 20 Count": "N/A",
        "First 20 Types": "N/A",
        "Dev Count": "N/A",
        "Fish Count": "N/A",
        "Shrimp Count": "N/A",
        "Whale Count": "N/A",
        "Fresh Count": "N/A",
        "Total First 20": "N/A",
        "Whale Percentage": "N/A",
        "Fish Percentage": "N/A",
        "Shrimp Percentage": "N/A",
        "Whale Fish Pattern": "N/A"
    }

    # Token Name
    token_name_match = re.search(r"\*\*\$([A-Za-z0-9]+)\*\*", message_text)
    if token_name_match:
        data["Token Name"] = token_name_match.group(1)

    # Warnings
    warnings = re.findall(r"(🚨|⚠️)?\s*__([^__]+)__", message_text)
    if warnings:
        filtered_warnings = [
            f"{icon} {warning}" if icon else warning
            for icon, warning in warnings
            if any(keyword.lower() in warning.lower()
                 for keyword in ["High", "Migrating", "Low", "Dev", "Sniped", "Abnormal"])
        ]
        if filtered_warnings:
            data["Warnings"] = " | ".join(filtered_warnings)

    # Age
    age_match = re.search(r"🕒 \*\*Age:\*\* (\d+[smhdw]+\s*\d*[smhdw]*)", message_text)
    if age_match:
        data["Age"] = age_match.group(1)

    # Market Cap
    mc_match = re.search(r"💰 \*\*MC:\*\* (\$\d[\d,.]*[KMB]?)", message_text)
    if mc_match:
        data["MC"] = mc_match.group(1)

    # T-MC
    t_mc_match = re.search(r"🔝 __(\$\d[\d,.]*[KMB]?)__", message_text)
    if t_mc_match:
        data["T-MC"] = t_mc_match.group(1)

    # Liquidity
    liq_match = re.search(r"💧 \*\*(v?Liq):\*\* (\$\d[\d,.]*[KMB]?) \((\d+\.?\d* SOL)\)", message_text)
    if liq_match:
        liq_type = liq_match.group(1)
        liq_value = liq_match.group(2)
        liq_sol = liq_match.group(3)
        if liq_type == "vLiq":
            liq_value += "-v"
        data["Liq"] = liq_value
        data["Liq SOL"] = liq_sol

    # Scans
    scans_match = re.search(r"⚡ \*\*Scans:\*\* (\d+)", message_text)
    if scans_match:
        data["Scans"] = scans_match.group(1)

    # Hodls
    hodls_match = re.search(r"\[\*\*Hodls\*\*\]\([^)]+\): (\d+)", message_text)
    if hodls_match:
        data["Hodls"] = hodls_match.group(1)

    # High
    high_match = re.search(r"┗ High:\s*\[(\d+\.?\d*%)]", message_text)
    if high_match:
        data["High"] = high_match.group(1)

    # Snipers
    snipers_match = re.search(r"🔫 \*\*Snipers:\*\* (\d+) • (\d+\.?\d*%)", message_text)
    if snipers_match:
        data["Snipers"] = snipers_match.group(1)
        data["Snipers Percentage"] = snipers_match.group(2)

    # Dev
    dev_match = re.search(r"🛠️ Dev.*?(\d+ SOL)", message_text)
    if dev_match:
        data["Dev"] = dev_match.group(1)

    # Sniped
    sniped_match = re.search(r"┣ Sniped: (\d+\.?\d*%)", message_text)
    if sniped_match:
        data["Sniped"] = sniped_match.group(1)

    # Top Holders
    top_holders_match = re.search(r"\[\*\*Hodls\*\*\]\([^)]+\): \d+ • Top: (\d+\.?\d*%)", message_text)
    if top_holders_match:
        data["Top Holders"] = top_holders_match.group(1)

    # LP
    lp_match = re.search(r"\*\*:\*\* (\d+%) Burnt", message_text)
    if lp_match:
        data["LP"] = lp_match.group(1)

    # First
    first_match = re.search(r"\*\*:\*\* (\d+)\s*Fresh", message_text)
    if first_match:
        data["First"] = first_match.group(1)

    # First Percentage
    first_percentage_match = re.search(r"Fresh\s*•\s*(\d+%)", message_text)
    if first_percentage_match:
        data["First Percentage"] = first_percentage_match.group(1)

    # Sold
    sold_match = re.search(r"\| Sold:\s*(\d+\.?\d*%)", message_text)
    if sold_match:
        data["Sold"] = sold_match.group(1)

    # Volume patterns
    vol_match = re.search(r"📈 \*\*Vol:\*\* ([^*\n]+)", message_text)
    if vol_match:
        data["Volume"] = vol_match.group(1).strip()

    # Price patterns
    price_match = re.search(r"📈 \*\*Price:\*\* ([^*\n]+)", message_text)
    if price_match:
        data["Price"] = price_match.group(1).strip()

    # Dex Paid patterns
    if "Paid✅" in message_text:
        data["Dex Paid"] = "✅"
    elif "❌ **Dex Paid**" in message_text or "❌ Dex Paid" in message_text:
        data["Dex Paid"] = "❌"

    # LP Burnt patterns
    lp_burnt_match = re.search(r"(\d+%) Burnt", message_text)
    if lp_burnt_match:
        data["LP Burnt"] = lp_burnt_match.group(1)

    # Mint Authority patterns
    mint_match = re.search(r"\*\*Mint\*\*[^:]*: ([^*\|]+)", message_text)
    if mint_match:
        data["Mint Authority"] = mint_match.group(1).strip()

    # Freeze Authority patterns
    freeze_match = re.search(r"🧊 \*\*Freeze\*\*[^:]*: ([^*\n]+)", message_text)
    if freeze_match:
        data["Freeze Authority"] = freeze_match.group(1).strip()

    # Bundled patterns
    bundled_match = re.search(r"Bundled: (\d+%)", message_text)
    if bundled_match:
        data["Bundled"] = bundled_match.group(1)

    # Airdrop patterns
    airdrop_match = re.search(r"Airdrop: (\d+%)", message_text)
    if airdrop_match:
        data["Airdrop"] = airdrop_match.group(1)

    # Burnt patterns (different from LP Burnt)
    burnt_match = re.search(r"Burnt: ([^🔥\n]+)", message_text)
    if burnt_match:
        data["Burnt"] = burnt_match.group(1).strip()

    # Made patterns
    made_match = re.search(r"Made: (\d+)", message_text)
    if made_match:
        data["Made"] = made_match.group(1)

    # Bond patterns
    bond_match = re.search(r"Bond: (\d+)", message_text)
    if bond_match:
        data["Bond"] = bond_match.group(1)

    # Best patterns
    best_match = re.search(r"Best: (\$[^\]]+)", message_text)
    if best_match:
        data["Best"] = best_match.group(1).strip()

    # First 20 detailed patterns
    first20_detailed = re.search(r"🎯 First 20[^:]*: (\d+%) \| (\d+) ([🛠🐟🍤🐳🌱]+) • (\d+%)", message_text)
    if first20_detailed:
        data["First 20 Percentage"] = first20_detailed.group(1)
        data["First 20 Count"] = first20_detailed.group(2)
        data["First 20 Types"] = first20_detailed.group(3)

    # Extract whale/fish patterns and analyze them
    whale_fish_patterns = extract_whale_fish_patterns(message_text)
    if whale_fish_patterns:
        data.update(whale_fish_patterns)

    return data

def extract_whale_fish_patterns(text):
    """Extract whale/fish emoji patterns and analyze them"""
    patterns = {}

    # Find lines with whale/fish emojis
    whale_fish_lines = re.findall(r'[🛠🐟🍤🐳🌱]+', text)

    if whale_fish_lines:
        # Combine all patterns
        all_patterns = ''.join(whale_fish_lines)
        patterns['Whale Fish Pattern'] = all_patterns

        # Count each type
        dev_count = all_patterns.count('🛠')
        fish_count = all_patterns.count('🐟')
        shrimp_count = all_patterns.count('🍤')
        whale_count = all_patterns.count('🐳')
        fresh_count = all_patterns.count('🌱')

        if dev_count > 0:
            patterns['Dev Count'] = str(dev_count)
        if fish_count > 0:
            patterns['Fish Count'] = str(fish_count)
        if shrimp_count > 0:
            patterns['Shrimp Count'] = str(shrimp_count)
        if whale_count > 0:
            patterns['Whale Count'] = str(whale_count)
        if fresh_count > 0:
            patterns['Fresh Count'] = str(fresh_count)

        # Calculate total and percentages
        total_holders = dev_count + fish_count + shrimp_count + whale_count + fresh_count
        if total_holders > 0:
            patterns['Total First 20'] = str(total_holders)
            if whale_count > 0:
                patterns['Whale Percentage'] = f"{(whale_count/total_holders)*100:.1f}%"
            if fish_count > 0:
                patterns['Fish Percentage'] = f"{(fish_count/total_holders)*100:.1f}%"
            if shrimp_count > 0:
                patterns['Shrimp Percentage'] = f"{(shrimp_count/total_holders)*100:.1f}%"

    return patterns

async def export_to_excel(data):
    """Export data to Excel with thread-safety."""
    async with processing_lock:
        try:
            wb = load_workbook(EXCEL_FILE)
            ws = wb.active

            # Check if token already exists
            token_exists = False
            for row_idx, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
                if row[1] == data.get("Token Address"):  # Token Address is in column 2
                    token_exists = True
                    logger.info(f"⏩ Token {data['Token Address']} already exists, skipping")
                    return

            if not token_exists:
                # Use Morocco time for timestamp
                morocco_time = datetime.now(MOROCCO_TZ).strftime("%Y-%m-%d %H:%M:%S")
                row = [
                    morocco_time,
                    data["Token Address"],
                    data["Token Name"],
                    data["Warnings"],
                    data["Age"],
                    data["MC"],
                    data["T-MC"],
                    data["Liq"],
                    data["Liq SOL"],
                    data["Volume"],
                    data["Price"],
                    data["Dex Paid"],
                    data["Scans"],
                    data["Hodls"],
                    data["High"],
                    data["Snipers"],
                    data["Snipers Percentage"],
                    data["Dev"],
                    data["Sniped"],
                    data["Top Holders"],
                    data["LP Burnt"],
                    data["Mint Authority"],
                    data["Freeze Authority"],
                    data["Bundled"],
                    data["Airdrop"],
                    data["Burnt"],
                    data["First"],
                    data["First Percentage"],
                    data["Sold"],
                    data["Made"],
                    data["Bond"],
                    data["Best"],
                    data["First 20 Percentage"],
                    data["First 20 Count"],
                    data["First 20 Types"],
                    data["Dev Count"],
                    data["Fish Count"],
                    data["Shrimp Count"],
                    data["Whale Count"],
                    data["Fresh Count"],
                    data["Total First 20"],
                    data["Whale Percentage"],
                    data["Fish Percentage"],
                    data["Shrimp Percentage"],
                    data["Whale Fish Pattern"]
                ]
                ws.append(row)
                logger.info(f"✅ Added new row for token {data['Token Address']}")

            wb.save(EXCEL_FILE)
            logger.info(f"✅ Data saved to {EXCEL_FILE}")
        except Exception as e:
            logger.error(f"❌ Failed to export data to Excel: {e}")

def read_tokens_from_file():
    """Read tokens from tokens.txt file"""
    tokens = []
    try:
        if os.path.exists("tokens.txt"):
            with open("tokens.txt", "r", encoding="utf-8") as f:
                for line in f:
                    token = line.strip()
                    if token and len(token) >= 32:
                        tokens.append(token)
            logger.info(f"📖 Read {len(tokens)} tokens from tokens.txt")
        else:
            logger.warning("⚠️ tokens.txt file not found")
    except Exception as e:
        logger.error(f"❌ Error reading tokens.txt: {e}")
    return tokens

async def send_tokens_to_bot():
    """Send all tokens from tokens.txt to @soul_scanner_bot and scrape data"""
    tokens = read_tokens_from_file()

    if not tokens:
        logger.warning("⚠️ No tokens found to send")
        return

    sent_count = 0
    for token in tokens:
        if token in processed_tokens:
            logger.info(f"⏩ Token {token[:10]}... already processed, skipping")
            continue

        try:
            logger.info(f"📤 Sending token to {DESTINATION_BOT}: {token}")
            await client.send_message(DESTINATION_BOT, token)
            processed_tokens.add(token)
            sent_count += 1

            # Wait longer for scanner bot to process and respond
            logger.info(f"⏳ Waiting for scanner bot response for token {token}...")
            await asyncio.sleep(5)  # Initial wait for bot to process

            response = None
            # Try to get the most recent message that contains our token
            for attempt in range(10):  # Try for up to 10 attempts (20 seconds total)
                await asyncio.sleep(2)  # Wait 2 seconds between each check

                async for msg in client.iter_messages(DESTINATION_BOT, limit=10):
                    # Check if message contains our token and has actual data (not just echo)
                    if msg.text and (
                        token in msg.text or
                        (len(token) > 10 and token[:10] in msg.text) or
                        (len(token) > 20 and token[:20] in msg.text)
                    ):
                        # Make sure it's not just echoing our input - look for scanner data indicators
                        scanner_indicators = ["**$", "MC:", "Age:", "Scans:", "Liq:", "💰", "🕒", "⚡", "💧", "**‎", "• **$"]
                        if any(indicator in msg.text for indicator in scanner_indicators):
                            response = msg
                            logger.info(f"🔍 Found scanner response for token {token} (attempt {attempt + 1})")
                            break
                        elif len(msg.text.strip()) == len(token):
                            # This might be just the token echo, continue waiting
                            logger.info(f"📝 Found token echo, waiting for scanner data... (attempt {attempt + 1})")
                        else:
                            # Found a message with the token but no clear scanner data
                            logger.info(f"📝 Found token mention but no scanner data yet (attempt {attempt + 1})")

                if response:
                    break

                logger.info(f"⏳ Still waiting for scanner data... (attempt {attempt + 1}/10)")

            if not response:
                logger.warning(f"⚠️ No scanner response found after 20 seconds, trying to get latest message...")
                # Last attempt - get the most recent message from the bot
                async for msg in client.iter_messages(DESTINATION_BOT, limit=3):
                    if msg.text and token in msg.text:
                        response = msg
                        logger.info(f"📄 Using latest message as fallback")
                        break

            if response:
                # Log the response for debugging
                logger.info(f"📄 Scanner response preview: {response.text[:200]}...")

                # Extract data from scanner response
                scanner_data = extract_data_from_scanner_response(response.text, token)

                # Log extracted data for debugging
                non_na_data = {k: v for k, v in scanner_data.items() if v != "N/A"}
                logger.info(f"📊 Extracted data: {non_na_data}")

                # Export to Excel
                await export_to_excel(scanner_data)
                logger.info(f"✅ Data extracted and saved for token {token}")
            else:
                logger.warning(f"⚠️ No response received for token {token}")
                # Still save the token with N/A data so we know it was processed
                empty_data = extract_data_from_scanner_response("", token)
                await export_to_excel(empty_data)

            # Rate limiting
            if sent_count < len(tokens):
                logger.info(f"⏳ Waiting {RATE_LIMIT_DELAY} seconds...")
                await asyncio.sleep(RATE_LIMIT_DELAY)

        except Exception as e:
            logger.error(f"❌ Error processing token {token}: {e}")

    logger.info(f"✅ Finished processing {sent_count} tokens with {DESTINATION_BOT}")

async def main():
    """Main function"""
    global client

    logger.info("🚀 Starting Token Sender & Data Scraper")
    logger.info(f"📍 Destination: {DESTINATION_BOT}")
    logger.info(f"📊 Excel File: {EXCEL_FILE}")
    logger.info("=" * 50)

    # Initialize Excel file
    initialize_excel()

    # Initialize Telegram client
    client = TelegramClient('session', API_ID, API_HASH)

    try:
        # Start the client
        await client.start(phone=PHONE)
        logger.info("✅ Client started successfully")

        # Send tokens
        await send_tokens_to_bot()

    except Exception as e:
        logger.error(f"❌ Error in main: {e}")
    finally:
        if client:
            await client.disconnect()
            logger.info("🔌 Client disconnected")

if __name__ == "__main__":
    asyncio.run(main())