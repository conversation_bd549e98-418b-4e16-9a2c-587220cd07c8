import re

# Test data from actual messages
test_message = """
[**🎯 First 20**](https://t.me/soul_scanner_bot?start=first20_3A8rXQZwtrdTLBMm7CwfHE8iq6rQXm2s8xScRp1epump)**:** 26% | 11 🐟 • 19%
[🛠](https://solscan.io/account/7QFx2nZC4ef7LRZiFtizfBmBWm1tE3Wa5yGGPe97rw6N)[🍤](https://solscan.io/account/32en2STpbeQ58zXqb5chxYK16X7EeHAKdAdLNnWjDbvf)[🌱](https://solscan.io/account/777GnhpiJwc8S3LgaBygYNhKp51fNwkQJMzxkCLDGDa8)[🌱](https://solscan.io/account/14bdjTjK2Y8rkCQVPHTp2XeKni8sxQE1Usned9m3Nsg6)[🐟](https://solscan.io/account/AVvPMvMmdLSqHpBSYmxkJSFSCqGsK1t9scfVVMunrDsk)[🍤](https://solscan.io/account/BRhuaa9dSrkgn3vu2Lzp8tmif3yzzmR8z5YeTdDDriZ8)[🐟](https://solscan.io/account/14XYJqnFtfqtEW7vNEy45mhGvYy4CxmaeN2oJ3ERtWD7)[🍤](https://solscan.io/account/8aaRWuPGNP1qoBk7jE9ZRMaUhNnCn1cujjUkv6KoEzVL)[🍤](https://solscan.io/account/3CAYRzRRoJN2TC27abkVo4przxTia2oFchH3Tu1TCGkE)[🐟](https://solscan.io/account/DCJ9xfTPK7wu1PE2i9sDy2jtpTcUhWJeR7eYPCzgyx2D)[🐟](https://solscan.io/account/3kBgfuYSc4SXQ8paGRv3VjKXsdKv1yUuRHSMAgXegRhG)[🐟](https://solscan.io/account/DpoABhL1fWmSixTSh6JsFoMFcGF7CjEhwDqjD4uw9yXq)[🍤](https://solscan.io/account/D3ygL8jYnAQ9MGEtohQby1m9zNXFxe6dmk5uQ7g5Xukt)[🐟](https://solscan.io/account/CD8XwGBw5x44ZWtvxcX4T2XYmiZhdKHdujeYcKKJuHbi)[🍤](https://solscan.io/account/95tgTqkvT8vqUFkoR3NwRCrqMcrJDbzAc5B8uf6aWMjj)[🐟](https://solscan.io/account/3p17Y5WftqMRLpNmjrCkoxYUUqrxQcp3e54k5YFkAaip)[🐟](https://solscan.io/account/G8dhLgzKLT25auGQrxbH2tHAt22MjGxAbvnyCVozpoVV)[🐟](https://solscan.io/account/3bskqo8ssyiA9PQbzcJkFZWJYZqWHGbPEuNeYskJcBDN)[🐟](https://solscan.io/account/5gmEEsHAFjyA2bADxGmUh8jRD5U1FRPyNTpj5YzprsNP)[🐟](https://solscan.io/account/BENE3RkN9qxCMjvHtJxWK1RApcQkmcExDPRvaLXfvmKh)
"""

def test_first20_extraction():
    """Test extraction of First 20 summary line"""
    print("Testing First 20 Summary Extraction:")
    print("=" * 50)

    # Debug: Show the actual line
    first20_line = re.search(r"🎯 First 20.*", test_message)
    if first20_line:
        print(f"Found line: {first20_line.group(0)}")

    # Extract from summary line: [**🎯 First 20**](link)**:** 26% | 11 🐟 • 19%
    first20_pattern = re.search(r"\*\*:\*\* (\d+%) \| (\d+) ([🛠🐟🍤🐳🌱]) • (\d+%)", test_message)
    
    if first20_pattern:
        percentage = first20_pattern.group(1)
        count = first20_pattern.group(2)
        emoji = first20_pattern.group(3)
        emoji_percentage = first20_pattern.group(4)
        
        print(f"First 20 Percentage: {percentage}")
        print(f"Count: {count}")
        print(f"Fish Column (emoji): {emoji}")
        print(f"Emoji Percentage: {emoji_percentage}")
    else:
        print("No First 20 pattern found")

def test_whale_fish_pattern_extraction():
    """Test extraction of detailed whale/fish pattern"""
    print("\nTesting Whale Fish Pattern Extraction:")
    print("=" * 50)
    
    # Extract detailed pattern from solscan links - format: [🛠](https://solscan.io/account/...)
    holder_patterns = re.findall(r'\[([🛠🐟🍤🐳🌱])\]\(https://solscan\.io/account/[^)]+\)', test_message)
    
    if holder_patterns:
        full_pattern = ''.join(holder_patterns)
        print(f"Whale Fish Pattern: {full_pattern}")
        print(f"Pattern Length: {len(full_pattern)}")
        
        # Count each type
        dev_count = full_pattern.count('🛠')
        fish_count = full_pattern.count('🐟')
        shrimp_count = full_pattern.count('🍤')
        whale_count = full_pattern.count('🐳')
        fresh_count = full_pattern.count('🌱')
        
        print(f"Dev Count (🛠): {dev_count}")
        print(f"Fish Count (🐟): {fish_count}")
        print(f"Shrimp Count (🍤): {shrimp_count}")
        print(f"Whale Count (🐳): {whale_count}")
        print(f"Fresh Count (🌱): {fresh_count}")
        print(f"Total: {dev_count + fish_count + shrimp_count + whale_count + fresh_count}")
    else:
        print("No whale fish pattern found")

def test_expected_results():
    """Show expected results"""
    print("\nExpected Results:")
    print("=" * 50)
    print("Fish Column: 🛠 (from '1 🛠 • 18%')")
    print("Whale Fish Pattern: 🛠🐟🍤🍤🍤🍤🍤🌱🍤🌱🐟🐟🐟🐟🐟🐟🐟🐟🍤🍤")
    print("Dev Count: 1")
    print("Fish Count: 8") 
    print("Shrimp Count: 9")
    print("Fresh Count: 2")

if __name__ == "__main__":
    test_first20_extraction()
    test_whale_fish_pattern_extraction()
    test_expected_results()
