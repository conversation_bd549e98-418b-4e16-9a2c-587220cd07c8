import re

# Test data from your example
test_message = """
🎯 First 20 (https://t.me/soul_scanner_bot?start=first20_3S6AQpGoNsHNH3VbWG65zvsuBaBGMGMbaCXbqBRTpump): 44% | 1 🛠 • 18% 
🛠 (https://solscan.io/account/GzuECMH8jvZCXcTAS3J2N8NodeMJWjeNRuTrS84Lhkih)🐟 (https://solscan.io/account/o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc)🍤 (https://solscan.io/account/6TP3Hh9m87XfK4EzpT4K2hfUii7fx7DsDp5S7WrXPspz)🍤 (https://solscan.io/account/FDPuzrjgujLGf2FiSapJ6qwC388yFSr5FhjNg8Ufd4RX)🍤 (https://solscan.io/account/CAwPsY9vb4Nyp5rQL5RP336u7rdVwfUDGRxX7zBmggSA)🍤 (https://solscan.io/account/23YqUHgXxHS4GgVjBubih5UN7zEFSY2QRph6FzFw4opR)🍤 (https://solscan.io/account/CJveikbmqF9qjY6gatZApFRWrTJxaoHbQ3ciDy1CQSY2)🌱 (https://solscan.io/account/7K1RU2VW97VgWxCkqHid9FSnFuViXM5wRosCd7Bp9L3P)🍤 (https://solscan.io/account/H89iD1vNRRLbGjDQDsk1hYuJNQdUfiVS98caoDx9Pggy)🌱 (https://solscan.io/account/5p3QJY84GM7QoEWuQJpy6qazADo1Ym3Kf8AuqnQYpWZs)
🐟 (https://solscan.io/account/DvbqrDXrY8u7coMsTYq8RVsUWoNuUMQFUb3B8g2nHcUs)🐟 (https://solscan.io/account/CJki3kek1rpyNXxMMHBdc7v5jSZW7jC5DRH6RPkVPSnk)🐟 (https://solscan.io/account/37VXEnjxCfd4WCzV8VP7D34MRugQJU5qosYPXqvHFg6m)🐟 (https://solscan.io/account/GEidvA34SYqdrJ32ZRkZTC6ntHXjfLDGCwfmFDwprHJt)🐟 (https://solscan.io/account/912b62csG64frkFEvg769h8yP1LQEp2PMgo8eYJ8xePx)🐟 (https://solscan.io/account/5SCuJoBbeuowUeAmeNnAyqNHPH2ASruQAPSLHTvJgEHB)🐟 (https://solscan.io/account/9PdDMqikJqY48xXtGZTojFfwX5wefJ4PUMyRJLcsjtM5)🐟 (https://solscan.io/account/5gavZ93e3YWYzYU9jYhBpo83n78dB1y8ou1irjnntt5m)🍤 (https://solscan.io/account/7a7XBK8c2kFxi9pKtdioSYa4S8K95QxUHeBpEwrCzY4w)🍤 (https://solscan.io/account/Dh585H1dvPyHEWE2SzdCZTBMEauRjqFLvu8ZBZheL8NQ)
"""

def test_first20_extraction():
    """Test extraction of First 20 summary line"""
    print("Testing First 20 Summary Extraction:")
    print("=" * 50)

    # Debug: Show the actual line
    first20_line = re.search(r"🎯 First 20.*", test_message)
    if first20_line:
        print(f"Found line: {first20_line.group(0)}")

    # Extract from summary line: "🎯 First 20 (...): 44% | 1 🛠 • 18%"
    first20_pattern = re.search(r"🎯 First 20.*?: (\d+%) \| (\d+) ([🛠🐟🍤🐳🌱]) • (\d+%)", test_message)
    
    if first20_pattern:
        percentage = first20_pattern.group(1)
        count = first20_pattern.group(2)
        emoji = first20_pattern.group(3)
        emoji_percentage = first20_pattern.group(4)
        
        print(f"First 20 Percentage: {percentage}")
        print(f"Count: {count}")
        print(f"Fish Column (emoji): {emoji}")
        print(f"Emoji Percentage: {emoji_percentage}")
    else:
        print("No First 20 pattern found")

def test_whale_fish_pattern_extraction():
    """Test extraction of detailed whale/fish pattern"""
    print("\nTesting Whale Fish Pattern Extraction:")
    print("=" * 50)
    
    # Extract detailed pattern from solscan links
    holder_patterns = re.findall(r'([🛠🐟🍤🐳🌱]) \(https://solscan\.io/account/[^)]+\)', test_message)
    
    if holder_patterns:
        full_pattern = ''.join(holder_patterns)
        print(f"Whale Fish Pattern: {full_pattern}")
        print(f"Pattern Length: {len(full_pattern)}")
        
        # Count each type
        dev_count = full_pattern.count('🛠')
        fish_count = full_pattern.count('🐟')
        shrimp_count = full_pattern.count('🍤')
        whale_count = full_pattern.count('🐳')
        fresh_count = full_pattern.count('🌱')
        
        print(f"Dev Count (🛠): {dev_count}")
        print(f"Fish Count (🐟): {fish_count}")
        print(f"Shrimp Count (🍤): {shrimp_count}")
        print(f"Whale Count (🐳): {whale_count}")
        print(f"Fresh Count (🌱): {fresh_count}")
        print(f"Total: {dev_count + fish_count + shrimp_count + whale_count + fresh_count}")
    else:
        print("No whale fish pattern found")

def test_expected_results():
    """Show expected results"""
    print("\nExpected Results:")
    print("=" * 50)
    print("Fish Column: 🛠 (from '1 🛠 • 18%')")
    print("Whale Fish Pattern: 🛠🐟🍤🍤🍤🍤🍤🌱🍤🌱🐟🐟🐟🐟🐟🐟🐟🐟🍤🍤")
    print("Dev Count: 1")
    print("Fish Count: 8") 
    print("Shrimp Count: 9")
    print("Fresh Count: 2")

if __name__ == "__main__":
    test_first20_extraction()
    test_whale_fish_pattern_extraction()
    test_expected_results()
