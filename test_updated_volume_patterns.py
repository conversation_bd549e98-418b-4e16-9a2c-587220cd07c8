import re

# Test data from actual scanner bot messages
test_message = """
**💊 **[**‎Pussy Cat**](https://pump.fun/6hxzowqwShQKqXptJjT3mvkYSNZuzQcVT6UDYwrcpump) • **$PUSSY**
`6hxzowqwShQKqXptJjT3mvkYSNZuzQcVT6UDYwrcpump`

🕒 **Age:** 3h
💰 **MC:** $4.7K  • 🔝 __$25.9K__
💧 **vLiq:** $9.6K (1.7 SOL)

📈 **Vol:** __1h__: $400 | __1d__: $97.3K

✅ **Dex Paid**
⚡ **Scans:** 82
**👥 **[**Hodls**](https://solscan.io/token/6hxzowqwShQKqXptJjT3mvkYSNZuzQcVT6UDYwrcpump#holders): 84 • Top: 5.2%

🔫 **Snipers:** 3 • 6.7% 🤍
[**🎯 First 20**](https://t.me/soul_scanner_bot?start=first20_6hxzowqwShQKqXptJjT3mvkYSNZuzQcVT6UDYwrcpump)**:** 16% | 10 🐟 • 12%
"""

def test_updated_patterns():
    """Test all updated extraction patterns"""
    print("Testing Updated Extraction Patterns:")
    print("=" * 50)
    
    # Test Volume pattern
    volume_match = re.search(r"📈 \*\*Vol:\*\* ([^📈\n]+)", test_message)
    if volume_match:
        volume_data = volume_match.group(1).strip()
        print(f"✅ Volume: '{volume_data}'")
        print(f"   Expected: '__1h__: $400 | __1d__: $97.3K'")
    else:
        print("❌ Volume: No match")
    
    # Test Scans pattern
    scans_match = re.search(r"⚡ \*\*Scans:\*\* (\d+)", test_message)
    if scans_match:
        print(f"✅ Scans: {scans_match.group(1)}")
        print(f"   Expected: 82")
    else:
        print("❌ Scans: No match")
    
    # Test Hodls pattern
    hodls_match = re.search(r"\*\*👥 \*\*\[\*\*Hodls\*\*\]\([^)]+\): ([\d,]+)", test_message)
    if hodls_match:
        print(f"✅ Hodls: {hodls_match.group(1)}")
        print(f"   Expected: 84")
    else:
        print("❌ Hodls: No match")
    
    # Test Top Holders pattern
    top_holders_match = re.search(r"\*\*👥 \*\*\[\*\*Hodls\*\*\]\([^)]+\): [\d,]+ • Top: (\d+\.?\d*%)", test_message)
    if top_holders_match:
        print(f"✅ Top Holders: {top_holders_match.group(1)}")
        print(f"   Expected: 5.2%")
    else:
        print("❌ Top Holders: No match")
    
    # Test First 20 pattern
    first20_match = re.search(r"\*\*:\*\* (\d+%) \| (\d+) ([🛠🐟🍤🐳🌱]) • (\d+%)", test_message)
    if first20_match:
        percentage = first20_match.group(1)
        count = first20_match.group(2)
        emoji = first20_match.group(3)
        emoji_percentage = first20_match.group(4)
        merged_first20 = f"{count} {emoji} • {emoji_percentage}"
        print(f"✅ First 20: '{merged_first20}'")
        print(f"   Expected: '10 🐟 • 12%'")
    else:
        print("❌ First 20: No match")
    
    # Test Dex Paid pattern
    if "✅" in test_message and "Dex" in test_message:
        print("✅ Dex Paid: ✅")
    elif "❌" in test_message and "Dex" in test_message:
        print("✅ Dex Paid: ❌")
    else:
        print("❌ Dex Paid: No match")

def test_nocat_message():
    """Test with NOCAT message that has higher volume"""
    print("\nTesting NOCAT Message (Higher Volume):")
    print("=" * 50)
    
    nocat_message = """
[🥈](https://t.me/solearlytrending) **‎no cat** • **$NOCAT**
`624Yj5LnRgJ9Wx8VGdS626Jj2uc4KSTQzAsnAsMopump`

🕒 **Age:** 4h
💰 **MC:** $117.5K • 🔝 __$312.1K__
💧 **Liq:** $38.1K (125 SOL)

📈 **Vol:** __1h__: $527.9K | __1d__: $1.4M
📈 **Price:** __1h__: -46%🔻 | __1d__: 85%🔼

⚡ **Scans:** 627 | 🔗 [X](https://x.com/i/communities/1929472228892291358)
**👥 **[**Hodls**](https://solscan.io/token/624Yj5LnRgJ9Wx8VGdS626Jj2uc4KSTQzAsnAsMopump#holders): 1,438 • Top: 15.5%
"""
    
    # Test Volume pattern
    volume_match = re.search(r"📈 \*\*Vol:\*\* ([^📈\n]+)", nocat_message)
    if volume_match:
        volume_data = volume_match.group(1).strip()
        print(f"✅ Volume: '{volume_data}'")
        print(f"   Expected: '__1h__: $527.9K | __1d__: $1.4M'")
    else:
        print("❌ Volume: No match")
    
    # Test Scans pattern
    scans_match = re.search(r"⚡ \*\*Scans:\*\* (\d+)", nocat_message)
    if scans_match:
        print(f"✅ Scans: {scans_match.group(1)}")
        print(f"   Expected: 627")
    else:
        print("❌ Scans: No match")
    
    # Test Hodls pattern with comma
    hodls_match = re.search(r"\*\*👥 \*\*\[\*\*Hodls\*\*\]\([^)]+\): ([\d,]+)", nocat_message)
    if hodls_match:
        print(f"✅ Hodls: {hodls_match.group(1)}")
        print(f"   Expected: 1,438")
    else:
        print("❌ Hodls: No match")
    
    # Test Top Holders pattern with comma
    top_holders_match = re.search(r"\*\*👥 \*\*\[\*\*Hodls\*\*\]\([^)]+\): [\d,]+ • Top: (\d+\.?\d*%)", nocat_message)
    if top_holders_match:
        print(f"✅ Top Holders: {top_holders_match.group(1)}")
        print(f"   Expected: 15.5%")
    else:
        print("❌ Top Holders: No match")

if __name__ == "__main__":
    test_updated_patterns()
    test_nocat_message()
