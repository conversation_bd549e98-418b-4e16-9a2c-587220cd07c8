from telethon import TelegramClient
import asyncio
import re
import json
from datetime import datetime

# Configuration
API_ID = "24841946"
API_HASH = "38d74f00caf1381396fc58a579e89a97"
PHONE = "+212762663516"
SCANNER_BOT = "@soul_scanner_bot"

async def fetch_and_analyze_messages():
    """Fetch recent messages from soul_scanner_bot and analyze patterns"""
    
    client = TelegramClient('session', API_ID, API_HASH)
    
    try:
        await client.start(phone=PHONE)
        print(f"✅ Connected to Telegram")
        print(f"🔍 Fetching messages from {SCANNER_BOT}...")
        print("=" * 80)
        
        messages_data = []
        message_count = 0
        
        # Fetch recent messages
        async for msg in client.iter_messages(SCANNER_BOT, limit=20):
            if msg.text:
                message_count += 1
                
                print(f"\n📨 MESSAGE #{message_count}")
                print(f"🕒 Time: {msg.date}")
                print(f"📝 Text Length: {len(msg.text)} characters")
                print("-" * 40)
                print(msg.text)
                print("-" * 40)
                
                # Analyze patterns in the message
                patterns_found = analyze_message_patterns(msg.text)
                if patterns_found:
                    print("🔍 PATTERNS FOUND:")
                    for pattern, value in patterns_found.items():
                        print(f"   {pattern}: {value}")
                
                # Store message data
                messages_data.append({
                    'timestamp': msg.date.isoformat(),
                    'text': msg.text,
                    'patterns': patterns_found
                })
                
                print("=" * 80)
        
        # Save to file for analysis
        with open('scanner_messages_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(messages_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Analyzed {message_count} messages")
        print(f"💾 Data saved to 'scanner_messages_analysis.json'")
        
        # Generate extraction patterns summary
        generate_extraction_summary(messages_data)
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        await client.disconnect()
        print("🔌 Disconnected")

def analyze_message_patterns(text):
    """Analyze a message and extract all possible data patterns"""
    patterns = {}
    
    # Token Name patterns
    token_name_patterns = [
        r"\*\*\$([A-Za-z0-9]+)\*\*",
        r"• \*\*\$([A-Za-z0-9]+)\*\*",
        r"\*\*‎([^*]+)\*\* • \*\*\$([A-Za-z0-9]+)\*\*"
    ]
    
    for pattern in token_name_patterns:
        match = re.search(pattern, text)
        if match:
            if len(match.groups()) == 1:
                patterns['Token Name'] = match.group(1)
            else:
                patterns['Project Name'] = match.group(1)
                patterns['Token Name'] = match.group(2)
            break
    
    # Age patterns
    age_match = re.search(r"🕒 \*\*Age:\*\* ([^*\n]+)", text)
    if age_match:
        patterns['Age'] = age_match.group(1).strip()
    
    # Market Cap patterns
    mc_match = re.search(r"💰 \*\*MC:\*\* (\$[^*•\n]+)", text)
    if mc_match:
        patterns['MC'] = mc_match.group(1).strip()
    
    # Top MC patterns
    t_mc_match = re.search(r"🔝 __(\$[^_]+)__", text)
    if t_mc_match:
        patterns['T-MC'] = t_mc_match.group(1).strip()
    
    # Liquidity patterns
    liq_patterns = [
        r"💧 \*\*(v?Liq):\*\* (\$[^*\(]+) \(([^)]+)\)",
        r"💧 \*\*Liq:\*\* (\$[^*\(]+) \(([^)]+)\)"
    ]
    
    for pattern in liq_patterns:
        match = re.search(pattern, text)
        if match:
            if len(match.groups()) == 3:
                liq_type = match.group(1)
                liq_value = match.group(2).strip()
                liq_sol = match.group(3).strip()
                if liq_type == "vLiq":
                    liq_value += "-v"
                patterns['Liq'] = liq_value
                patterns['Liq SOL'] = liq_sol
            break
    
    # Volume patterns
    vol_match = re.search(r"📈 \*\*Vol:\*\* ([^*\n]+)", text)
    if vol_match:
        patterns['Volume'] = vol_match.group(1).strip()
    
    # Scans patterns
    scans_match = re.search(r"⚡ \*\*Scans:\*\* (\d+)", text)
    if scans_match:
        patterns['Scans'] = scans_match.group(1)
    
    # Holders patterns
    hodls_match = re.search(r"\[\*\*Hodls\*\*\][^:]*: (\d+)", text)
    if hodls_match:
        patterns['Hodls'] = hodls_match.group(1)
    
    # Top Holders percentage
    top_holders_match = re.search(r"Top: (\d+\.?\d*%)", text)
    if top_holders_match:
        patterns['Top Holders'] = top_holders_match.group(1)
    
    # Snipers patterns
    snipers_match = re.search(r"🔫 \*\*Snipers:\*\* (\d+) • (\d+\.?\d*%)", text)
    if snipers_match:
        patterns['Snipers'] = snipers_match.group(1)
        patterns['Snipers Percentage'] = snipers_match.group(2)
    
    # Dev patterns
    dev_match = re.search(r"🛠️ Dev[^:]*: ([^*\n]+)", text)
    if dev_match:
        patterns['Dev'] = dev_match.group(1).strip()
    
    # Warnings patterns
    warnings = re.findall(r"(🚨|⚠️)?\s*__([^__]+)__", text)
    if warnings:
        warning_list = []
        for icon, warning in warnings:
            if icon:
                warning_list.append(f"{icon} {warning}")
            else:
                warning_list.append(warning)
        patterns['Warnings'] = " | ".join(warning_list)
    
    # Authority patterns
    mint_match = re.search(r"\*\*Mint\*\*[^:]*: ([^*\|]+)", text)
    if mint_match:
        patterns['Mint Authority'] = mint_match.group(1).strip()
    
    freeze_match = re.search(r"🧊 Freeze[^:]*: ([^*\n]+)", text)
    if freeze_match:
        patterns['Freeze Authority'] = freeze_match.group(1).strip()
    
    # LP patterns
    lp_match = re.search(r"(\d+%) Burnt", text)
    if lp_match:
        patterns['LP Burnt'] = lp_match.group(1)
    
    # Fresh/First patterns
    fresh_match = re.search(r"(\d+) Fresh", text)
    if fresh_match:
        patterns['Fresh'] = fresh_match.group(1)
    
    fresh_percentage_match = re.search(r"Fresh • (\d+%)", text)
    if fresh_percentage_match:
        patterns['Fresh Percentage'] = fresh_percentage_match.group(1)
    
    # Sold patterns
    sold_match = re.search(r"Sold: (\d+\.?\d*%)", text)
    if sold_match:
        patterns['Sold'] = sold_match.group(1)
    
    # High patterns
    high_match = re.search(r"High: \[(\d+\.?\d*%)\]", text)
    if high_match:
        patterns['High'] = high_match.group(1)
    
    # Token address patterns
    token_addr_match = re.search(r"`([A-Za-z0-9]{43,44})`", text)
    if token_addr_match:
        patterns['Token Address'] = token_addr_match.group(1)
    
    # Dex Paid pattern
    if "Dex Paid" in text:
        patterns['Dex Paid'] = "❌" if "❌" in text else "✅"
    
    return patterns

def generate_extraction_summary(messages_data):
    """Generate a summary of all patterns found across messages"""
    
    all_patterns = {}
    
    for msg_data in messages_data:
        for pattern, value in msg_data['patterns'].items():
            if pattern not in all_patterns:
                all_patterns[pattern] = []
            all_patterns[pattern].append(value)
    
    print(f"\n📊 EXTRACTION PATTERNS SUMMARY")
    print("=" * 50)
    
    for pattern, values in all_patterns.items():
        unique_values = list(set(values))[:5]  # Show first 5 unique values
        print(f"{pattern}: {len(values)} occurrences")
        print(f"   Examples: {unique_values}")
        print()
    
    # Save patterns to file
    with open('extraction_patterns.json', 'w', encoding='utf-8') as f:
        json.dump(all_patterns, f, indent=2, ensure_ascii=False)
    
    print(f"💾 Pattern analysis saved to 'extraction_patterns.json'")

if __name__ == "__main__":
    print("🚀 Soul Scanner Bot Message Fetcher & Analyzer")
    print("=" * 50)
    asyncio.run(fetch_and_analyze_messages())
