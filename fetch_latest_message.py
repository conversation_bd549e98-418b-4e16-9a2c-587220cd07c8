from telethon import TelegramClient
import asyncio

# Configuration
API_ID = "24841946"
API_HASH = "38d74f00caf1381396fc58a579e89a97"
PHONE = "+212762663516"
SCANNER_BOT = "@soul_scanner_bot"

async def fetch_latest_message():
    """Fetch and display the latest message from soul_scanner_bot"""
    
    client = TelegramClient('session', API_ID, API_HASH)
    
    try:
        await client.start(phone=PHONE)
        print(f"✅ Connected to Telegram")
        print(f"🔍 Fetching latest message from {SCANNER_BOT}...")
        print("=" * 80)
        
        # Get the latest message
        async for msg in client.iter_messages(SCANNER_BOT, limit=1):
            if msg.text:
                print(f"🕒 Time: {msg.date}")
                print(f"📝 Message Length: {len(msg.text)} characters")
                print("-" * 80)
                print("📄 FULL MESSAGE:")
                print(msg.text)
                print("-" * 80)
                
                # Save to file for easy copying
                with open('latest_scanner_message.txt', 'w', encoding='utf-8') as f:
                    f.write(f"Time: {msg.date}\n")
                    f.write(f"Length: {len(msg.text)} characters\n")
                    f.write("-" * 40 + "\n")
                    f.write(msg.text)
                
                print("💾 Message saved to 'latest_scanner_message.txt'")
                break
        else:
            print("❌ No messages found")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        await client.disconnect()
        print("🔌 Disconnected")

if __name__ == "__main__":
    print("🚀 Soul Scanner Bot - Latest Message Fetcher")
    print("=" * 50)
    asyncio.run(fetch_latest_message())
