from telethon import TelegramClient
import asyncio
import re
import json

# Configuration
API_ID = "24841946"
API_HASH = "38d74f00caf1381396fc58a579e89a97"
PHONE = "+212762663516"
SCANNER_BOT = "@soul_scanner_bot"

async def fetch_first20_patterns():
    """Fetch last 5 messages and analyze First 20 patterns"""
    
    client = TelegramClient('session', API_ID, API_HASH)
    
    try:
        await client.start(phone=PHONE)
        print(f"✅ Connected to Telegram")
        print(f"🔍 Fetching last 5 messages from {SCANNER_BOT}...")
        print("=" * 80)
        
        messages_with_first20 = []
        message_count = 0
        
        # Fetch recent messages
        async for msg in client.iter_messages(SCANNER_BOT, limit=5):
            if msg.text and len(msg.text) > 100:  # Only analyze substantial messages
                message_count += 1
                
                print(f"\n📨 MESSAGE #{message_count}")
                print(f"🕒 Time: {msg.date}")
                print("-" * 40)
                print(f"📝 Full Message:")
                print(msg.text)
                print("-" * 40)
                
                # Check for First 20 patterns
                first20_data = analyze_first20_patterns(msg.text)
                
                if first20_data:
                    print("🎯 FIRST 20 PATTERNS FOUND:")
                    for key, value in first20_data.items():
                        print(f"   {key}: {value}")
                    
                    messages_with_first20.append({
                        'timestamp': msg.date.isoformat(),
                        'first20_data': first20_data,
                        'full_message': msg.text
                    })
                else:
                    print("   ❌ No First 20 patterns found")
                
                print("=" * 80)
        
        # Save analysis
        with open('first20_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(messages_with_first20, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Analyzed {message_count} messages")
        print(f"📊 Found {len(messages_with_first20)} messages with First 20 data")
        print(f"💾 Analysis saved to 'first20_analysis.json'")
        
        # Generate extraction code
        if messages_with_first20:
            generate_extraction_code(messages_with_first20)
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        await client.disconnect()
        print("🔌 Disconnected")

def analyze_first20_patterns(text):
    """Analyze message for First 20 patterns"""
    patterns = {}
    
    # Look for First 20 summary line
    first20_summary = re.search(r"🎯 First 20.*?: (\d+%) \| (\d+) ([🛠🐟🍤🐳🌱]) • (\d+%)", text)
    if first20_summary:
        patterns['First 20 Percentage'] = first20_summary.group(1)
        patterns['First 20 Count'] = first20_summary.group(2)
        patterns['Fish'] = first20_summary.group(3)
        patterns['Fish Percentage'] = first20_summary.group(4)
        print(f"   ✅ Found summary: {first20_summary.group(0)}")
    
    # Look for detailed holder pattern with solscan links
    holder_patterns = re.findall(r'([🛠🐟🍤🐳🌱]) \(https://solscan\.io/account/[^)]+\)', text)
    if holder_patterns:
        full_pattern = ''.join(holder_patterns)
        patterns['Whale Fish Pattern'] = full_pattern
        patterns['Pattern Length'] = len(full_pattern)
        
        # Count each type
        dev_count = full_pattern.count('🛠')
        fish_count = full_pattern.count('🐟')
        shrimp_count = full_pattern.count('🍤')
        whale_count = full_pattern.count('🐳')
        fresh_count = full_pattern.count('🌱')
        
        patterns['Dev Count'] = dev_count
        patterns['Fish Count'] = fish_count
        patterns['Shrimp Count'] = shrimp_count
        patterns['Whale Count'] = whale_count
        patterns['Fresh Count'] = fresh_count
        patterns['Total Holders'] = dev_count + fish_count + shrimp_count + whale_count + fresh_count
        
        print(f"   ✅ Found pattern: {full_pattern}")
        print(f"   📊 Counts: 🛠{dev_count} 🐟{fish_count} 🍤{shrimp_count} 🐳{whale_count} 🌱{fresh_count}")
    
    # Look for alternative First 20 patterns
    alt_first20 = re.search(r"First 20.*?(\d+%)", text)
    if alt_first20 and 'First 20 Percentage' not in patterns:
        patterns['Alt First 20'] = alt_first20.group(0)
    
    # Look for any emoji sequences that might be holder patterns
    emoji_sequences = re.findall(r'[🛠🐟🍤🐳🌱]{5,}', text)
    if emoji_sequences and 'Whale Fish Pattern' not in patterns:
        patterns['Emoji Sequences'] = emoji_sequences
    
    return patterns

def generate_extraction_code(messages_data):
    """Generate updated extraction code based on found patterns"""
    print(f"\n🔧 EXTRACTION CODE SUGGESTIONS:")
    print("=" * 50)
    
    # Analyze all patterns found
    all_summary_patterns = []
    all_detail_patterns = []
    
    for msg_data in messages_data:
        first20_data = msg_data['first20_data']
        
        if 'Fish' in first20_data:
            # Extract the actual pattern from the message
            msg_text = msg_data['full_message']
            summary_line = re.search(r"🎯 First 20.*", msg_text)
            if summary_line:
                all_summary_patterns.append(summary_line.group(0))
        
        if 'Whale Fish Pattern' in first20_data:
            all_detail_patterns.append(first20_data['Whale Fish Pattern'])
    
    if all_summary_patterns:
        print("📋 Summary Line Examples:")
        for i, pattern in enumerate(all_summary_patterns[:3], 1):
            print(f"   {i}. {pattern}")
    
    if all_detail_patterns:
        print("\n🐟 Pattern Examples:")
        for i, pattern in enumerate(all_detail_patterns[:3], 1):
            print(f"   {i}. {pattern}")
    
    # Suggest regex patterns
    print(f"\n💡 SUGGESTED REGEX PATTERNS:")
    print("For summary line:")
    print("   r\"🎯 First 20.*?: (\\d+%) \\| (\\d+) ([🛠🐟🍤🐳🌱]) • (\\d+%)\"")
    print("\nFor detailed pattern:")
    print("   r\"([🛠🐟🍤🐳🌱]) \\(https://solscan\\.io/account/[^)]+\\)\"")

if __name__ == "__main__":
    print("🚀 Soul Scanner Bot - First 20 Pattern Analyzer")
    print("=" * 50)
    asyncio.run(fetch_first20_patterns())
