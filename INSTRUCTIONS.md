# Token Sender Bot

A simple Telegram bot that reads Solana token addresses from a file and sends them to a destination bot.

## Files

- `token_sender.py` - Main script that sends tokens to @soul_scanner_bot
- `tokens.txt` - Text file containing token addresses (one per line)

## Setup

1. Make sure you have the required dependencies:
   ```bash
   pip install telethon
   ```

2. Add your token addresses to `tokens.txt` (one per line)

3. Run the script:
   ```bash
   python token_sender.py
   ```

4. On first run, you'll need to authenticate with your phone number and verification code

## Configuration

The script is configured to send tokens to `@soul_scanner_bot`. You can modify the `destination_username` variable in the script to change the destination.

Rate limiting is set to 2 seconds between each token to avoid hitting Telegram's limits.

## How it works

1. The script reads token addresses from `tokens.txt`
2. It connects to Telegram using your account
3. For each token, it sends a message to the destination bot
4. It keeps track of processed tokens to avoid duplicates
5. It includes rate limiting to respect Telegram's API limits

## Example tokens.txt

```
7GCihgDB8fe6KNjn2MqiGQW7LAp6deQUDJk3g6fvRHvC
So11111111111111111111111111111111111111112
EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v
```

Each line should contain one Solana token address.
