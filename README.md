# Solbix - Token Scanner & Data Scraper

A powerful Python script that automatically sends token addresses to the Soul Scanner bot on Telegram and extracts comprehensive data into an Excel spreadsheet with advanced features like T-MC color coding and High data extraction.

## Features

✅ **Comprehensive Data Extraction**: Token name, age, market cap, liquidity, volume, price, scans, holders, and more  
✅ **T-MC Color Coding**: Visual indicators for market cap targets (Yellow: $30K-$60K, Green: $61K-$100K, Blue: $101K+)  
✅ **High Data Extraction**: Extracts `┗ High: [5.2%](link)` format data  
✅ **Volume Tracking**: Real-time 1h and 1d volume data  
✅ **First 20 Analysis**: Merged format showing count, emoji, and percentage  
✅ **Whale Fish Patterns**: Complete emoji sequences for holder analysis  
✅ **Custom Column Order**: Prioritized data layout for efficient analysis  
✅ **Environment Configuration**: Secure configuration using .env file  
✅ **Rate Limiting**: Prevents API abuse with configurable delays  
✅ **Duplicate Prevention**: Automatically skips already processed tokens  

## Installation

1. **Clone or download the project files**
2. **Install required packages:**
   ```bash
   pip install telethon openpyxl python-dotenv pytz
   ```

## Configuration

### 1. Environment Variables (.env file)

Create a `.env` file in the project directory with your configuration:

```env
# Telegram API Configuration
API_ID=your_api_id
API_HASH=your_api_hash
PHONE=your_phone_number

# Bot Configuration
SCANNER_BOT=@soul_scanner_bot

# File Paths
SESSION_FILE=session.session
EXCEL_FILE=solbix_data.xlsx
TOKENS_FILE=tokens.txt
LOG_FILE=solbix.log

# Script Settings
RATE_LIMIT_DELAY=2
RESPONSE_TIMEOUT=30
```

### 2. Getting Telegram API Credentials

1. Go to [my.telegram.org](https://my.telegram.org)
2. Log in with your phone number
3. Go to "API Development Tools"
4. Create a new application
5. Copy your `API_ID` and `API_HASH`

### 3. Token Input File

Create a `tokens.txt` file with one token address per line:
```
624Yj5LnRgJ9Wx8VGdS626Jj2uc4KSTQzAsnAsMopump
6hxzowqwShQKqXptJjT3mvkYSNZuzQcVT6UDYwrcpump
6Mf6NTet2CR8yzwkaY7WaoVfNSQFm2xTSzzUTRYBociZ
```

## Usage

1. **Configure your .env file** with your Telegram credentials
2. **Add token addresses** to `tokens.txt`
3. **Run the script:**
   ```bash
   python Solbix.py
   ```
4. **Check results** in `solbix_data.xlsx`

## Excel Output

### Column Order (Priority Columns 1-20):
1. **Timestamp** - Morocco timezone
2. **Token Address** - Solana token address
3. **Token Name** - Token symbol
4. **Warnings** - Security alerts (High Holder, Dev Bundled, etc.)
5. **Age** - Token age
6. **MC** - Current market cap
7. **T-MC** - Target market cap (with color coding)
8. **Liq** - Liquidity amount
9. **Liq SOL** - Liquidity in SOL
10. **First 20 Percentage** - First 20 holders percentage
11. **First 20** - Merged format: `8 🐟 • 17%`
12. **Whale Fish Pattern** - Complete emoji sequence
13. **Made** - Developer performance count
14. **Volume** - 1h and 1d volume data
15. **Price** - Price change data
16. **Scans** - Number of scans
17. **Hodls** - Number of holders
18. **Top Holders** - Top holder percentage
19. **Snipers** - Sniper count
20. **Snipers Percentage** - Sniper percentage

### Additional Columns (21-29):
21. **Dex Paid** - DEX payment status
22. **High** - High holder data (`┗ High: 5.2%`)
23. **Dev** - Developer SOL amount
24. **LP Burnt** - LP burn percentage
25. **Bundled** - Bundle percentage
26. **Airdrop** - Airdrop percentage
27. **Burnt** - Burn percentage
28. **Sold** - Sold percentage
29. **Bond** - Bond count

### T-MC Color Coding:
- 🟡 **Yellow**: $30K - $60K
- 🟢 **Light Green**: $61K - $100K  
- 🔵 **Light Blue**: $101K+
- ⚪ **No Color**: Below $30K

## Configuration Options

### Environment Variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `API_ID` | Telegram API ID | Required |
| `API_HASH` | Telegram API Hash | Required |
| `PHONE` | Phone number with country code | Required |
| `SCANNER_BOT` | Scanner bot username | `@soul_scanner_bot` |
| `SESSION_FILE` | Telegram session file | `session.session` |
| `EXCEL_FILE` | Output Excel file | `solbix_data.xlsx` |
| `TOKENS_FILE` | Input tokens file | `tokens.txt` |
| `LOG_FILE` | Log file path | `solbix.log` |
| `RATE_LIMIT_DELAY` | Delay between tokens (seconds) | `2` |
| `RESPONSE_TIMEOUT` | Response timeout (seconds) | `30` |

## File Structure

```
project/
├── .env                 # Environment configuration
├── Solbix.py           # Main script
├── tokens.txt          # Input token addresses
├── session.session     # Telegram session (auto-generated)
├── solbix_data.xlsx    # Output Excel file
├── solbix.log          # Log file
└── README.md           # This file
```

## Security Notes

- ⚠️ **Never commit your .env file** to version control
- 🔒 **Keep your API credentials secure**
- 📱 **Use a dedicated phone number** for API access if possible
- 🛡️ **The session file contains authentication data** - keep it secure

## Troubleshooting

### Common Issues:

1. **"Invalid phone number"**: Ensure phone number includes country code (e.g., `+**********`)
2. **"Session file not found"**: The script will create it on first run
3. **"No tokens found"**: Check that `tokens.txt` exists and contains valid addresses
4. **"Rate limit exceeded"**: Increase `RATE_LIMIT_DELAY` in .env file

### Logs:
Check `solbix.log` for detailed execution logs and error messages.

## License

This project is for educational and personal use only.
