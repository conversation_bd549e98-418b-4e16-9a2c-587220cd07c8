# Solana Token Tracker (Solbix)

A comprehensive Solana token monitoring system that tracks multiple Telegram channels for token information and exports detailed analytics to Excel with real-time data processing and filtering capabilities.

## Features

- **Multi-Channel Monitoring**: Tracks multiple Telegram channels simultaneously
- **Dual Message Processing**: Handles two types of token messages with different extraction patterns
- **Excel Export**: Comprehensive data export with 29+ columns of token analytics
- **Real-time Processing**: Processes messages in real-time with queue management
- **Data Filtering**: Advanced filtering based on token criteria (age, market cap, liquidity, etc.)
- **Color-coded Excel**: Profit/loss visualization with conditional formatting
- **Session Persistence**: Maintains session state across restarts
- **Rate Limiting**: Built-in rate limiting to prevent API abuse
- **Error Recovery**: Robust error handling with retry mechanisms

## Project Structure

```
├── solbix.py                    # Main application script
├── solbix_data.xlsx            # Excel output file (auto-generated)
├── session_state.pickle        # Session persistence file
├── solbix_data.session         # Telegram session file
└── README.md                   # Project documentation
```

## Data Tracking

The system tracks comprehensive token data including:

### Basic Information
- Timestamp, Token Address, Token Name
- Called By, Calls, Profit, Trailing Sell

### Market Data
- Market Cap (MC), Target Market Cap (T-MC)
- Liquidity (Liq), Liquidity SOL
- Age, High, Made

### Security & Analysis
- Warnings, Snipers, Snipers Percentage
- Dev wallet info, Top Holders
- LP (Liquidity Pool), First buyer data
- Scans, Hodls, Fish data

### URLs & References
- X (Twitter) URLs
- Bonding Curve information

## Setup

### Prerequisites

- Python 3.7 or higher
- Telegram API credentials
- Access to monitored channels
- Excel-compatible software for viewing results

### Installation

1. Install required dependencies:
   ```bash
   pip install telethon openpyxl pytz
   ```

2. Configure your Telegram API credentials in the script:
   ```python
   API_ID = "your_api_id"
   API_HASH = "your_api_hash"
   PHONE = "your_phone_number"
   ```

### Configuration

The script monitors these channels by default:
- **Source Channel**: `@solbix` (Type 1 messages)
- **Tracker Channel**: `@solbix_tracker` (Type 2 messages)

## Message Types

### Type 1 Messages ("Make a call here")
- Extracts tokens from Bubblemap URLs in parentheses
- Pattern: `(https://bubblemaps.io/sol/token/[token_address])`
- Processes comprehensive token analytics

### Type 2 Messages ("is up", "has dropped")
- Extracts tokens from GeckoTerminal and Helius XRay URLs
- Patterns: 
  - `https://geckoterminal.com/solana/pools/[token_id]`
  - `https://xray.helius.xyz/token/[token_address]`
- Updates existing token data with price movements

## Token Filtering

Tokens are filtered based on:
- **Age**: Must be ≤ 24 hours old
- **Market Cap**: Must be ≤ $10M
- **Liquidity**: Must be ≥ $1K
- **Snipers**: Must be ≤ 80%
- **Warnings**: Excludes tokens with critical warnings

## Excel Output

### Color Coding
- **Green**: Profit > 35%
- **Light Green**: Profit 10-35%
- **Yellow**: Profit 0-10%
- **Light Red**: Loss 0-35%
- **Red**: Loss > 35%

### Data Columns
The Excel file contains 29 columns with comprehensive token analytics including market data, security metrics, and performance indicators.

## Usage

### Running the Application

```bash
python solbix.py
```

The application will:
1. Initialize Excel file with headers
2. Load previous session state
3. Connect to Telegram
4. Start monitoring configured channels
5. Process messages in real-time
6. Export data to Excel with formatting

### Monitoring

The application provides detailed console logging:
- Connection status
- Message processing
- Token extraction and filtering
- Excel export confirmation
- Error handling and recovery

## Advanced Features

### Session Management
- Automatic session persistence
- Recovery from disconnections
- Missed message detection and processing

### Queue Processing
- Asynchronous message processing
- Priority-based queue management
- Rate limiting and retry mechanisms

### Data Persistence
- Real-time Excel updates
- Session state backup
- Duplicate token handling

## Configuration Options

### Rate Limiting
```python
RATE_LIMIT_DELAY = 2  # seconds between requests
MAX_RETRIES = 3       # retry attempts for failed requests
```

### Filtering Criteria
```python
# Customize filtering thresholds
MAX_AGE_HOURS = 24
MAX_MARKET_CAP = ********  # $10M
MIN_LIQUIDITY = 1000       # $1K
MAX_SNIPERS_PERCENT = 80   # 80%
```

## Troubleshooting

- **Connection Issues**: Check API credentials and network connectivity
- **Missing Data**: Verify channel access permissions
- **Excel Errors**: Ensure file is not open in another application
- **Rate Limiting**: Adjust RATE_LIMIT_DELAY if experiencing API limits

## Security Notes

- Keep API credentials secure
- Monitor rate limits to avoid account restrictions
- Regularly backup Excel data and session files

## License

This project is for educational and research purposes only.
