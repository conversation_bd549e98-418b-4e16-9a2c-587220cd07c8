import re

# Test data with your specific volume format
test_message = """
📈 Vol: 1h: $8.6K | 1d: $559.8K

❌ Dex Paid
⚡️ Scans: 47 | 🔗 X (https://x.com/DogemonOnSol)•WEB (https://dogemononsol.xyz/)
👥 Hodls (https://solscan.io/token/BDw1Z9Dz2AkzMJPmiMQTB7yeVqQrb2uPCLMFT78Bpump#holders): 36 • Top: 13.6%
┗ High: 5.2% (https://solscan.io/account/2PdJ1PnjA8UTWamAh5PEev7nouVBairLvxAXSCfNGTJt)
"""

def test_volume_extraction():
    """Test volume extraction with your specific format"""
    print("Testing Volume Extraction:")
    print("=" * 50)
    
    # Current pattern from the script
    volume_match = re.search(r"📈 Vol: ([^⚡❌\n]+)", test_message)
    if volume_match:
        volume_data = volume_match.group(1).strip()
        print(f"✅ Volume extracted: '{volume_data}'")
        print(f"✅ Expected: '1h: $8.6K | 1d: $559.8K'")
        
        if volume_data == "1h: $8.6K | 1d: $559.8K":
            print("🎉 PERFECT MATCH!")
        else:
            print("⚠️ Format difference detected")
    else:
        print("❌ Volume: No match found")
    
    # Test alternative patterns if needed
    print("\nTesting alternative patterns:")
    
    # Pattern 1: More specific
    alt_pattern1 = re.search(r"📈 Vol: (1h: \$[^|]+ \| 1d: \$[^\n]+)", test_message)
    if alt_pattern1:
        print(f"✅ Alt Pattern 1: '{alt_pattern1.group(1).strip()}'")
    
    # Pattern 2: Capture everything after "Vol: "
    alt_pattern2 = re.search(r"📈 Vol: (.+?)(?=\n|$)", test_message)
    if alt_pattern2:
        print(f"✅ Alt Pattern 2: '{alt_pattern2.group(1).strip()}'")

def test_all_patterns():
    """Test all patterns together"""
    print("\nTesting All Patterns Together:")
    print("=" * 50)
    
    # Volume
    volume_match = re.search(r"📈 Vol: ([^⚡❌\n]+)", test_message)
    if volume_match:
        print(f"Volume: {volume_match.group(1).strip()}")
    
    # Scans
    scans_match = re.search(r"⚡️ Scans: (\d+)", test_message)
    if scans_match:
        print(f"Scans: {scans_match.group(1)}")
    
    # Hodls
    hodls_match = re.search(r"👥 Hodls \([^)]+\): (\d+)", test_message)
    if hodls_match:
        print(f"Hodls: {hodls_match.group(1)}")
    
    # Top Holders
    top_holders_match = re.search(r"👥 Hodls \([^)]+\): \d+ • Top: (\d+\.?\d*%)", test_message)
    if top_holders_match:
        print(f"Top Holders: {top_holders_match.group(1)}")
    
    # High
    high_match = re.search(r"┗ High: (\d+\.?\d*%)", test_message)
    if high_match:
        print(f"High: {high_match.group(1)}")
    
    # Dex Paid
    if "❌" in test_message and "Dex" in test_message:
        print("Dex Paid: ❌")
    elif "✅" in test_message and "Dex" in test_message:
        print("Dex Paid: ✅")

if __name__ == "__main__":
    test_volume_extraction()
    test_all_patterns()
